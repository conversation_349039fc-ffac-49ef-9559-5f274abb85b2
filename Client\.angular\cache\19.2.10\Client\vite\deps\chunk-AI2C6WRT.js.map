{"version": 3, "sources": ["../../../../../../node_modules/primeng/fesm2022/primeng-checkbox.mjs"], "sourcesContent": ["import * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, forwardRef, EventEmitter, signal, inject, booleanAttribute, numberAttribute, ContentChildren, ContentChild, ViewChild, Output, Input, ViewEncapsulation, ChangeDetectionStrategy, Component, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR, NgControl } from '@angular/forms';\nimport { contains, equals } from '@primeuix/utils';\nimport { SharedModule, PrimeTemplate } from 'primeng/api';\nimport { BaseComponent } from 'primeng/basecomponent';\nimport { CheckIcon, MinusIcon } from 'primeng/icons';\nimport { BaseStyle } from 'primeng/base';\nconst _c0 = [\"checkboxicon\"];\nconst _c1 = [\"input\"];\nconst _c2 = () => ({\n  \"p-checkbox-input\": true\n});\nconst _c3 = a0 => ({\n  checked: a0,\n  class: \"p-checkbox-icon\"\n});\nfunction Checkbox_ng_container_4_ng_container_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 8);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.checkboxIcon);\n    i0.ɵɵattribute(\"data-pc-section\", \"icon\");\n  }\n}\nfunction Checkbox_ng_container_4_ng_container_1_CheckIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"CheckIcon\", 9);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-checkbox-icon\");\n    i0.ɵɵattribute(\"data-pc-section\", \"icon\");\n  }\n}\nfunction Checkbox_ng_container_4_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Checkbox_ng_container_4_ng_container_1_span_1_Template, 1, 2, \"span\", 7)(2, Checkbox_ng_container_4_ng_container_1_CheckIcon_2_Template, 1, 2, \"CheckIcon\", 6);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.checkboxIcon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.checkboxIcon);\n  }\n}\nfunction Checkbox_ng_container_4_MinusIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"MinusIcon\", 9);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-checkbox-icon\");\n    i0.ɵɵattribute(\"data-pc-section\", \"icon\");\n  }\n}\nfunction Checkbox_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Checkbox_ng_container_4_ng_container_1_Template, 3, 2, \"ng-container\", 4)(2, Checkbox_ng_container_4_MinusIcon_2_Template, 1, 2, \"MinusIcon\", 6);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.checked);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1._indeterminate());\n  }\n}\nfunction Checkbox_5_ng_template_0_Template(rf, ctx) {}\nfunction Checkbox_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Checkbox_5_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nconst theme = ({\n  dt\n}) => `\n.p-checkbox {\n    position: relative;\n    display: inline-flex;\n    user-select: none;\n    vertical-align: bottom;\n    width: ${dt('checkbox.width')};\n    height: ${dt('checkbox.height')};\n}\n\n.p-checkbox-input {\n    cursor: pointer;\n    appearance: none;\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    padding: 0;\n    margin: 0;\n    opacity: 0;\n    z-index: 1;\n    outline: 0 none;\n    border: 1px solid transparent;\n    border-radius: ${dt('checkbox.border.radius')};\n}\n\n.p-checkbox-box {\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    border-radius: ${dt('checkbox.border.radius')};\n    border: 1px solid ${dt('checkbox.border.color')};\n    background: ${dt('checkbox.background')};\n    width: ${dt('checkbox.width')};\n    height: ${dt('checkbox.height')};\n    transition: background ${dt('checkbox.transition.duration')}, color ${dt('checkbox.transition.duration')}, border-color ${dt('checkbox.transition.duration')}, box-shadow ${dt('checkbox.transition.duration')}, outline-color ${dt('checkbox.transition.duration')};\n    outline-color: transparent;\n    box-shadow: ${dt('checkbox.shadow')};\n}\n\n.p-checkbox-icon {\n    transition-duration: ${dt('checkbox.transition.duration')};\n    color: ${dt('checkbox.icon.color')};\n    font-size: ${dt('checkbox.icon.size')};\n    width: ${dt('checkbox.icon.size')};\n    height: ${dt('checkbox.icon.size')};\n}\n\n.p-checkbox:not(.p-disabled):has(.p-checkbox-input:hover) .p-checkbox-box {\n    border-color: ${dt('checkbox.hover.border.color')};\n}\n\n.p-checkbox-checked .p-checkbox-box {\n    border-color: ${dt('checkbox.checked.border.color')};\n    background: ${dt('checkbox.checked.background')};\n}\n\n.p-checkbox-checked .p-checkbox-icon {\n    color: ${dt('checkbox.icon.checked.color')};\n}\n\n.p-checkbox-checked:not(.p-disabled):has(.p-checkbox-input:hover) .p-checkbox-box {\n    background: ${dt('checkbox.checked.hover.background')};\n    border-color: ${dt('checkbox.checked.hover.border.color')};\n}\n\n.p-checkbox-checked:not(.p-disabled):has(.p-checkbox-input:hover) .p-checkbox-icon {\n    color: ${dt('checkbox.icon.checked.hover.color')};\n}\n\n.p-checkbox:not(.p-disabled):has(.p-checkbox-input:focus-visible) .p-checkbox-box {\n    border-color: ${dt('checkbox.focus.border.color')};\n    box-shadow: ${dt('checkbox.focus.ring.shadow')};\n    outline: ${dt('checkbox.focus.ring.width')} ${dt('checkbox.focus.ring.style')} ${dt('checkbox.focus.ring.color')};\n    outline-offset: ${dt('checkbox.focus.ring.offset')};\n}\n\n.p-checkbox-checked:not(.p-disabled):has(.p-checkbox-input:focus-visible) .p-checkbox-box {\n    border-color: ${dt('checkbox.checked.focus.border.color')};\n}\n\np-checkBox.ng-invalid.ng-dirty .p-checkbox-box,\np-check-box.ng-invalid.ng-dirty .p-checkbox-box,\np-checkbox.ng-invalid.ng-dirty .p-checkbox-box {\n    border-color: ${dt('checkbox.invalid.border.color')};\n}\n\n.p-checkbox.p-variant-filled .p-checkbox-box {\n    background: ${dt('checkbox.filled.background')};\n}\n\n.p-checkbox-checked.p-variant-filled .p-checkbox-box {\n    background: ${dt('checkbox.checked.background')};\n}\n\n.p-checkbox-checked.p-variant-filled:not(.p-disabled):has(.p-checkbox-input:hover) .p-checkbox-box {\n    background: ${dt('checkbox.checked.hover.background')};\n}\n\n.p-checkbox.p-disabled {\n    opacity: 1;\n}\n\n.p-checkbox.p-disabled .p-checkbox-box {\n    background: ${dt('checkbox.disabled.background')};\n    border-color: ${dt('checkbox.checked.disabled.border.color')};\n}\n\n.p-checkbox.p-disabled .p-checkbox-box .p-checkbox-icon {\n    color: ${dt('checkbox.icon.disabled.color')};\n}\n\n.p-checkbox-sm,\n.p-checkbox-sm .p-checkbox-box {\n    width: ${dt('checkbox.sm.width')};\n    height: ${dt('checkbox.sm.height')};\n}\n\n.p-checkbox-sm .p-checkbox-icon {\n    font-size: ${dt('checkbox.icon.sm.size')};\n    width: ${dt('checkbox.icon.sm.size')};\n    height: ${dt('checkbox.icon.sm.size')};\n}\n\n.p-checkbox-lg,\n.p-checkbox-lg .p-checkbox-box {\n    width: ${dt('checkbox.lg.width')};\n    height: ${dt('checkbox.lg.height')};\n}\n\n.p-checkbox-lg .p-checkbox-icon {\n    font-size: ${dt('checkbox.icon.lg.size')};\n    width: ${dt('checkbox.icon.lg.size')};\n    height: ${dt('checkbox.icon.lg.size')};\n}\n`;\nconst classes = {\n  root: ({\n    instance,\n    props\n  }) => ['p-checkbox p-component', {\n    'p-checkbox-checked': instance.checked,\n    'p-disabled': props.disabled,\n    'p-invalid': props.invalid,\n    'p-variant-filled': props.variant ? props.variant === 'filled' : instance.config.inputStyle === 'filled' || instance.config.inputVariant === 'filled'\n  }],\n  box: 'p-checkbox-box',\n  input: 'p-checkbox-input',\n  icon: 'p-checkbox-icon'\n};\nclass CheckboxStyle extends BaseStyle {\n  name = 'checkbox';\n  theme = theme;\n  classes = classes;\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵCheckboxStyle_BaseFactory;\n    return function CheckboxStyle_Factory(__ngFactoryType__) {\n      return (ɵCheckboxStyle_BaseFactory || (ɵCheckboxStyle_BaseFactory = i0.ɵɵgetInheritedFactory(CheckboxStyle)))(__ngFactoryType__ || CheckboxStyle);\n    };\n  })();\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: CheckboxStyle,\n    factory: CheckboxStyle.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CheckboxStyle, [{\n    type: Injectable\n  }], null, null);\n})();\n/**\n *\n * Checkbox is an extension to standard checkbox element with theming.\n *\n * [Live Demo](https://www.primeng.org/checkbox/)\n *\n * @module checkboxstyle\n *\n */\nvar CheckboxClasses;\n(function (CheckboxClasses) {\n  /**\n   * Class name of the root element\n   */\n  CheckboxClasses[\"root\"] = \"p-checkbox\";\n  /**\n   * Class name of the box element\n   */\n  CheckboxClasses[\"box\"] = \"p-checkbox-box\";\n  /**\n   * Class name of the input element\n   */\n  CheckboxClasses[\"input\"] = \"p-checkbox-input\";\n  /**\n   * Class name of the icon element\n   */\n  CheckboxClasses[\"icon\"] = \"p-checkbox-icon\";\n})(CheckboxClasses || (CheckboxClasses = {}));\nconst CHECKBOX_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => Checkbox),\n  multi: true\n};\n/**\n * Checkbox is an extension to standard checkbox element with theming.\n * @group Components\n */\nclass Checkbox extends BaseComponent {\n  /**\n   * Value of the checkbox.\n   * @group Props\n   */\n  value;\n  /**\n   * Name of the checkbox group.\n   * @group Props\n   */\n  name;\n  /**\n   * When present, it specifies that the element should be disabled.\n   * @group Props\n   */\n  disabled;\n  /**\n   * Allows to select a boolean value instead of multiple values.\n   * @group Props\n   */\n  binary;\n  /**\n   * Establishes relationships between the component and label(s) where its value should be one or more element IDs.\n   * @group Props\n   */\n  ariaLabelledBy;\n  /**\n   * Used to define a string that labels the input element.\n   * @group Props\n   */\n  ariaLabel;\n  /**\n   * Index of the element in tabbing order.\n   * @group Props\n   */\n  tabindex;\n  /**\n   * Identifier of the focus input to match a label defined for the component.\n   * @group Props\n   */\n  inputId;\n  /**\n   * Inline style of the component.\n   * @group Props\n   */\n  style;\n  /**\n   * Inline style of the input element.\n   * @group Props\n   */\n  inputStyle;\n  /**\n   * Style class of the component.\n   * @group Props\n   */\n  styleClass;\n  /**\n   * Style class of the input element.\n   * @group Props\n   */\n  inputClass;\n  /**\n   * When present, it specifies input state as indeterminate.\n   * @group Props\n   */\n  indeterminate = false;\n  /**\n   * Defines the size of the component.\n   * @group Props\n   */\n  size;\n  /**\n   * Form control value.\n   * @group Props\n   */\n  formControl;\n  /**\n   * Icon class of the checkbox icon.\n   * @group Props\n   */\n  checkboxIcon;\n  /**\n   * When present, it specifies that the component cannot be edited.\n   * @group Props\n   */\n  readonly;\n  /**\n   * When present, it specifies that checkbox must be checked before submitting the form.\n   * @group Props\n   */\n  required;\n  /**\n   * When present, it specifies that the component should automatically get focus on load.\n   * @group Props\n   */\n  autofocus;\n  /**\n   * Value in checked state.\n   * @group Props\n   */\n  trueValue = true;\n  /**\n   * Value in unchecked state.\n   * @group Props\n   */\n  falseValue = false;\n  /**\n   * Specifies the input variant of the component.\n   * @group Props\n   */\n  variant;\n  /**\n   * Callback to invoke on value change.\n   * @param {CheckboxChangeEvent} event - Custom value change event.\n   * @group Emits\n   */\n  onChange = new EventEmitter();\n  /**\n   * Callback to invoke when the receives focus.\n   * @param {Event} event - Browser event.\n   * @group Emits\n   */\n  onFocus = new EventEmitter();\n  /**\n   * Callback to invoke when the loses focus.\n   * @param {Event} event - Browser event.\n   * @group Emits\n   */\n  onBlur = new EventEmitter();\n  inputViewChild;\n  get checked() {\n    return this._indeterminate() ? false : this.binary ? this.model === this.trueValue : contains(this.value, this.model);\n  }\n  get containerClass() {\n    return {\n      'p-checkbox p-component': true,\n      'p-checkbox-checked p-highlight': this.checked,\n      'p-disabled': this.disabled,\n      'p-variant-filled': this.variant === 'filled' || this.config.inputStyle() === 'filled' || this.config.inputVariant() === 'filled',\n      'p-checkbox-sm p-inputfield-sm': this.size === 'small',\n      'p-checkbox-lg p-inputfield-lg': this.size === 'large'\n    };\n  }\n  _indeterminate = signal(undefined);\n  /**\n   * The template of the checkbox icon.\n   * @group Templates\n   */\n  checkboxIconTemplate;\n  templates;\n  _checkboxIconTemplate;\n  model;\n  onModelChange = () => {};\n  onModelTouched = () => {};\n  focused = false;\n  _componentStyle = inject(CheckboxStyle);\n  ngAfterContentInit() {\n    this.templates.forEach(item => {\n      switch (item.getType()) {\n        case 'icon':\n          this._checkboxIconTemplate = item.template;\n          break;\n        case 'checkboxicon':\n          this._checkboxIconTemplate = item.template;\n          break;\n      }\n    });\n  }\n  ngOnChanges(changes) {\n    super.ngOnChanges(changes);\n    if (changes.indeterminate) {\n      this._indeterminate.set(changes.indeterminate.currentValue);\n    }\n  }\n  updateModel(event) {\n    let newModelValue;\n    /*\n     * When `formControlName` or `formControl` is used - `writeValue` is not called after control changes.\n     * Otherwise it is causing multiple references to the actual value: there is one array reference inside the component and another one in the control value.\n     * `selfControl` is the source of truth of references, it is made to avoid reference loss.\n     * */\n    const selfControl = this.injector.get(NgControl, null, {\n      optional: true,\n      self: true\n    });\n    const currentModelValue = selfControl && !this.formControl ? selfControl.value : this.model;\n    if (!this.binary) {\n      if (this.checked || this._indeterminate()) newModelValue = currentModelValue.filter(val => !equals(val, this.value));else newModelValue = currentModelValue ? [...currentModelValue, this.value] : [this.value];\n      this.onModelChange(newModelValue);\n      this.model = newModelValue;\n      if (this.formControl) {\n        this.formControl.setValue(newModelValue);\n      }\n    } else {\n      newModelValue = this._indeterminate() ? this.trueValue : this.checked ? this.falseValue : this.trueValue;\n      this.model = newModelValue;\n      this.onModelChange(newModelValue);\n    }\n    if (this._indeterminate()) {\n      this._indeterminate.set(false);\n    }\n    this.onChange.emit({\n      checked: newModelValue,\n      originalEvent: event\n    });\n  }\n  handleChange(event) {\n    if (!this.readonly) {\n      this.updateModel(event);\n    }\n  }\n  onInputFocus(event) {\n    this.focused = true;\n    this.onFocus.emit(event);\n  }\n  onInputBlur(event) {\n    this.focused = false;\n    this.onBlur.emit(event);\n    this.onModelTouched();\n  }\n  focus() {\n    this.inputViewChild.nativeElement.focus();\n  }\n  writeValue(model) {\n    this.model = model;\n    this.cd.markForCheck();\n  }\n  registerOnChange(fn) {\n    this.onModelChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onModelTouched = fn;\n  }\n  setDisabledState(val) {\n    setTimeout(() => {\n      this.disabled = val;\n      this.cd.markForCheck();\n    });\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵCheckbox_BaseFactory;\n    return function Checkbox_Factory(__ngFactoryType__) {\n      return (ɵCheckbox_BaseFactory || (ɵCheckbox_BaseFactory = i0.ɵɵgetInheritedFactory(Checkbox)))(__ngFactoryType__ || Checkbox);\n    };\n  })();\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: Checkbox,\n    selectors: [[\"p-checkbox\"], [\"p-checkBox\"], [\"p-check-box\"]],\n    contentQueries: function Checkbox_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, _c0, 4);\n        i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.checkboxIconTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n      }\n    },\n    viewQuery: function Checkbox_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c1, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.inputViewChild = _t.first);\n      }\n    },\n    inputs: {\n      value: \"value\",\n      name: \"name\",\n      disabled: [2, \"disabled\", \"disabled\", booleanAttribute],\n      binary: [2, \"binary\", \"binary\", booleanAttribute],\n      ariaLabelledBy: \"ariaLabelledBy\",\n      ariaLabel: \"ariaLabel\",\n      tabindex: [2, \"tabindex\", \"tabindex\", numberAttribute],\n      inputId: \"inputId\",\n      style: \"style\",\n      inputStyle: \"inputStyle\",\n      styleClass: \"styleClass\",\n      inputClass: \"inputClass\",\n      indeterminate: [2, \"indeterminate\", \"indeterminate\", booleanAttribute],\n      size: \"size\",\n      formControl: \"formControl\",\n      checkboxIcon: \"checkboxIcon\",\n      readonly: [2, \"readonly\", \"readonly\", booleanAttribute],\n      required: [2, \"required\", \"required\", booleanAttribute],\n      autofocus: [2, \"autofocus\", \"autofocus\", booleanAttribute],\n      trueValue: \"trueValue\",\n      falseValue: \"falseValue\",\n      variant: \"variant\"\n    },\n    outputs: {\n      onChange: \"onChange\",\n      onFocus: \"onFocus\",\n      onBlur: \"onBlur\"\n    },\n    features: [i0.ɵɵProvidersFeature([CHECKBOX_VALUE_ACCESSOR, CheckboxStyle]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature],\n    decls: 6,\n    vars: 29,\n    consts: [[\"input\", \"\"], [3, \"ngClass\"], [\"type\", \"checkbox\", 3, \"focus\", \"blur\", \"change\", \"value\", \"checked\", \"disabled\", \"readonly\", \"ngClass\"], [1, \"p-checkbox-box\"], [4, \"ngIf\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [3, \"styleClass\", 4, \"ngIf\"], [\"class\", \"p-checkbox-icon\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"p-checkbox-icon\", 3, \"ngClass\"], [3, \"styleClass\"]],\n    template: function Checkbox_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵelementStart(0, \"div\", 1)(1, \"input\", 2, 0);\n        i0.ɵɵlistener(\"focus\", function Checkbox_Template_input_focus_1_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onInputFocus($event));\n        })(\"blur\", function Checkbox_Template_input_blur_1_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onInputBlur($event));\n        })(\"change\", function Checkbox_Template_input_change_1_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.handleChange($event));\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(3, \"div\", 3);\n        i0.ɵɵtemplate(4, Checkbox_ng_container_4_Template, 3, 2, \"ng-container\", 4)(5, Checkbox_5_Template, 1, 0, null, 5);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵstyleMap(ctx.style);\n        i0.ɵɵclassMap(ctx.styleClass);\n        i0.ɵɵproperty(\"ngClass\", ctx.containerClass);\n        i0.ɵɵattribute(\"data-p-highlight\", ctx.checked)(\"data-p-checked\", ctx.checked)(\"data-p-disabled\", ctx.disabled);\n        i0.ɵɵadvance();\n        i0.ɵɵstyleMap(ctx.inputStyle);\n        i0.ɵɵclassMap(ctx.inputClass);\n        i0.ɵɵproperty(\"value\", ctx.value)(\"checked\", ctx.checked)(\"disabled\", ctx.disabled)(\"readonly\", ctx.readonly)(\"ngClass\", i0.ɵɵpureFunction0(26, _c2));\n        i0.ɵɵattribute(\"id\", ctx.inputId)(\"name\", ctx.name)(\"tabindex\", ctx.tabindex)(\"required\", ctx.required ? true : null)(\"aria-labelledby\", ctx.ariaLabelledBy)(\"aria-label\", ctx.ariaLabel);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", !ctx.checkboxIconTemplate && !ctx._checkboxIconTemplate);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.checkboxIconTemplate || ctx._checkboxIconTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(27, _c3, ctx.checked));\n      }\n    },\n    dependencies: [CommonModule, i1.NgClass, i1.NgIf, i1.NgTemplateOutlet, CheckIcon, MinusIcon, SharedModule],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Checkbox, [{\n    type: Component,\n    args: [{\n      selector: 'p-checkbox, p-checkBox, p-check-box',\n      standalone: true,\n      imports: [CommonModule, CheckIcon, MinusIcon, SharedModule],\n      template: `\n        <div [style]=\"style\" [class]=\"styleClass\" [ngClass]=\"containerClass\" [attr.data-p-highlight]=\"checked\" [attr.data-p-checked]=\"checked\" [attr.data-p-disabled]=\"disabled\">\n            <input\n                #input\n                [attr.id]=\"inputId\"\n                type=\"checkbox\"\n                [value]=\"value\"\n                [attr.name]=\"name\"\n                [checked]=\"checked\"\n                [attr.tabindex]=\"tabindex\"\n                [disabled]=\"disabled\"\n                [readonly]=\"readonly\"\n                [attr.required]=\"required ? true : null\"\n                [attr.aria-labelledby]=\"ariaLabelledBy\"\n                [attr.aria-label]=\"ariaLabel\"\n                [style]=\"inputStyle\"\n                [class]=\"inputClass\"\n                [ngClass]=\"{ 'p-checkbox-input': true }\"\n                (focus)=\"onInputFocus($event)\"\n                (blur)=\"onInputBlur($event)\"\n                (change)=\"handleChange($event)\"\n            />\n            <div class=\"p-checkbox-box\">\n                <ng-container *ngIf=\"!checkboxIconTemplate && !_checkboxIconTemplate\">\n                    <ng-container *ngIf=\"checked\">\n                        <span *ngIf=\"checkboxIcon\" class=\"p-checkbox-icon\" [ngClass]=\"checkboxIcon\" [attr.data-pc-section]=\"'icon'\"></span>\n                        <CheckIcon *ngIf=\"!checkboxIcon\" [styleClass]=\"'p-checkbox-icon'\" [attr.data-pc-section]=\"'icon'\" />\n                    </ng-container>\n                    <MinusIcon *ngIf=\"_indeterminate()\" [styleClass]=\"'p-checkbox-icon'\" [attr.data-pc-section]=\"'icon'\" />\n                </ng-container>\n                <ng-template *ngTemplateOutlet=\"checkboxIconTemplate || _checkboxIconTemplate; context: { checked: checked, class: 'p-checkbox-icon' }\"></ng-template>\n            </div>\n        </div>\n    `,\n      providers: [CHECKBOX_VALUE_ACCESSOR, CheckboxStyle],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None\n    }]\n  }], null, {\n    value: [{\n      type: Input\n    }],\n    name: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    binary: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    ariaLabelledBy: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input\n    }],\n    tabindex: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    inputId: [{\n      type: Input\n    }],\n    style: [{\n      type: Input\n    }],\n    inputStyle: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    inputClass: [{\n      type: Input\n    }],\n    indeterminate: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    size: [{\n      type: Input\n    }],\n    formControl: [{\n      type: Input\n    }],\n    checkboxIcon: [{\n      type: Input\n    }],\n    readonly: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    required: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    autofocus: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    trueValue: [{\n      type: Input\n    }],\n    falseValue: [{\n      type: Input\n    }],\n    variant: [{\n      type: Input\n    }],\n    onChange: [{\n      type: Output\n    }],\n    onFocus: [{\n      type: Output\n    }],\n    onBlur: [{\n      type: Output\n    }],\n    inputViewChild: [{\n      type: ViewChild,\n      args: ['input']\n    }],\n    checkboxIconTemplate: [{\n      type: ContentChild,\n      args: ['checkboxicon', {\n        descendants: false\n      }]\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [PrimeTemplate]\n    }]\n  });\n})();\nclass CheckboxModule {\n  static ɵfac = function CheckboxModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CheckboxModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: CheckboxModule,\n    imports: [Checkbox, SharedModule],\n    exports: [Checkbox, SharedModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [Checkbox, SharedModule, SharedModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CheckboxModule, [{\n    type: NgModule,\n    args: [{\n      imports: [Checkbox, SharedModule],\n      exports: [Checkbox, SharedModule]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CHECKBOX_VALUE_ACCESSOR, Checkbox, CheckboxClasses, CheckboxModule, CheckboxStyle };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUA,IAAM,MAAM,CAAC,cAAc;AAC3B,IAAM,MAAM,CAAC,OAAO;AACpB,IAAM,MAAM,OAAO;AAAA,EACjB,oBAAoB;AACtB;AACA,IAAM,MAAM,SAAO;AAAA,EACjB,SAAS;AAAA,EACT,OAAO;AACT;AACA,SAAS,uDAAuD,IAAI,KAAK;AACvE,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,CAAC;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,WAAW,OAAO,YAAY;AAC5C,IAAG,YAAY,mBAAmB,MAAM;AAAA,EAC1C;AACF;AACA,SAAS,4DAA4D,IAAI,KAAK;AAC5E,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,aAAa,CAAC;AAAA,EAChC;AACA,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,cAAc,iBAAiB;AAC7C,IAAG,YAAY,mBAAmB,MAAM;AAAA,EAC1C;AACF;AACA,SAAS,gDAAgD,IAAI,KAAK;AAChE,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,wDAAwD,GAAG,GAAG,QAAQ,CAAC,EAAE,GAAG,6DAA6D,GAAG,GAAG,aAAa,CAAC;AAC9K,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,YAAY;AACzC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,YAAY;AAAA,EAC5C;AACF;AACA,SAAS,6CAA6C,IAAI,KAAK;AAC7D,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,aAAa,CAAC;AAAA,EAChC;AACA,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,cAAc,iBAAiB;AAC7C,IAAG,YAAY,mBAAmB,MAAM;AAAA,EAC1C;AACF;AACA,SAAS,iCAAiC,IAAI,KAAK;AACjD,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,iDAAiD,GAAG,GAAG,gBAAgB,CAAC,EAAE,GAAG,8CAA8C,GAAG,GAAG,aAAa,CAAC;AAChK,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,OAAO;AACpC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,eAAe,CAAC;AAAA,EAC/C;AACF;AACA,SAAS,kCAAkC,IAAI,KAAK;AAAC;AACrD,SAAS,oBAAoB,IAAI,KAAK;AACpC,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,mCAAmC,GAAG,GAAG,aAAa;AAAA,EACzE;AACF;AACA,IAAM,QAAQ,CAAC;AAAA,EACb;AACF,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aAMO,GAAG,gBAAgB,CAAC;AAAA,cACnB,GAAG,iBAAiB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,qBAiBd,GAAG,wBAAwB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,qBAO5B,GAAG,wBAAwB,CAAC;AAAA,wBACzB,GAAG,uBAAuB,CAAC;AAAA,kBACjC,GAAG,qBAAqB,CAAC;AAAA,aAC9B,GAAG,gBAAgB,CAAC;AAAA,cACnB,GAAG,iBAAiB,CAAC;AAAA,6BACN,GAAG,8BAA8B,CAAC,WAAW,GAAG,8BAA8B,CAAC,kBAAkB,GAAG,8BAA8B,CAAC,gBAAgB,GAAG,8BAA8B,CAAC,mBAAmB,GAAG,8BAA8B,CAAC;AAAA;AAAA,kBAErP,GAAG,iBAAiB,CAAC;AAAA;AAAA;AAAA;AAAA,2BAIZ,GAAG,8BAA8B,CAAC;AAAA,aAChD,GAAG,qBAAqB,CAAC;AAAA,iBACrB,GAAG,oBAAoB,CAAC;AAAA,aAC5B,GAAG,oBAAoB,CAAC;AAAA,cACvB,GAAG,oBAAoB,CAAC;AAAA;AAAA;AAAA;AAAA,oBAIlB,GAAG,6BAA6B,CAAC;AAAA;AAAA;AAAA;AAAA,oBAIjC,GAAG,+BAA+B,CAAC;AAAA,kBACrC,GAAG,6BAA6B,CAAC;AAAA;AAAA;AAAA;AAAA,aAItC,GAAG,6BAA6B,CAAC;AAAA;AAAA;AAAA;AAAA,kBAI5B,GAAG,mCAAmC,CAAC;AAAA,oBACrC,GAAG,qCAAqC,CAAC;AAAA;AAAA;AAAA;AAAA,aAIhD,GAAG,mCAAmC,CAAC;AAAA;AAAA;AAAA;AAAA,oBAIhC,GAAG,6BAA6B,CAAC;AAAA,kBACnC,GAAG,4BAA4B,CAAC;AAAA,eACnC,GAAG,2BAA2B,CAAC,IAAI,GAAG,2BAA2B,CAAC,IAAI,GAAG,2BAA2B,CAAC;AAAA,sBAC9F,GAAG,4BAA4B,CAAC;AAAA;AAAA;AAAA;AAAA,oBAIlC,GAAG,qCAAqC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAMzC,GAAG,+BAA+B,CAAC;AAAA;AAAA;AAAA;AAAA,kBAIrC,GAAG,4BAA4B,CAAC;AAAA;AAAA;AAAA;AAAA,kBAIhC,GAAG,6BAA6B,CAAC;AAAA;AAAA;AAAA;AAAA,kBAIjC,GAAG,mCAAmC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAQvC,GAAG,8BAA8B,CAAC;AAAA,oBAChC,GAAG,wCAAwC,CAAC;AAAA;AAAA;AAAA;AAAA,aAInD,GAAG,8BAA8B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,aAKlC,GAAG,mBAAmB,CAAC;AAAA,cACtB,GAAG,oBAAoB,CAAC;AAAA;AAAA;AAAA;AAAA,iBAIrB,GAAG,uBAAuB,CAAC;AAAA,aAC/B,GAAG,uBAAuB,CAAC;AAAA,cAC1B,GAAG,uBAAuB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,aAK5B,GAAG,mBAAmB,CAAC;AAAA,cACtB,GAAG,oBAAoB,CAAC;AAAA;AAAA;AAAA;AAAA,iBAIrB,GAAG,uBAAuB,CAAC;AAAA,aAC/B,GAAG,uBAAuB,CAAC;AAAA,cAC1B,GAAG,uBAAuB,CAAC;AAAA;AAAA;AAGzC,IAAM,UAAU;AAAA,EACd,MAAM,CAAC;AAAA,IACL;AAAA,IACA;AAAA,EACF,MAAM,CAAC,0BAA0B;AAAA,IAC/B,sBAAsB,SAAS;AAAA,IAC/B,cAAc,MAAM;AAAA,IACpB,aAAa,MAAM;AAAA,IACnB,oBAAoB,MAAM,UAAU,MAAM,YAAY,WAAW,SAAS,OAAO,eAAe,YAAY,SAAS,OAAO,iBAAiB;AAAA,EAC/I,CAAC;AAAA,EACD,KAAK;AAAA,EACL,OAAO;AAAA,EACP,MAAM;AACR;AACA,IAAM,gBAAN,MAAM,uBAAsB,UAAU;AAAA,EACpC,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,sBAAsB,mBAAmB;AACvD,cAAQ,+BAA+B,6BAAgC,sBAAsB,cAAa,IAAI,qBAAqB,cAAa;AAAA,IAClJ;AAAA,EACF,GAAG;AAAA,EACH,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,eAAc;AAAA,EACzB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAUH,IAAI;AAAA,CACH,SAAUA,kBAAiB;AAI1B,EAAAA,iBAAgB,MAAM,IAAI;AAI1B,EAAAA,iBAAgB,KAAK,IAAI;AAIzB,EAAAA,iBAAgB,OAAO,IAAI;AAI3B,EAAAA,iBAAgB,MAAM,IAAI;AAC5B,GAAG,oBAAoB,kBAAkB,CAAC,EAAE;AAC5C,IAAM,0BAA0B;AAAA,EAC9B,SAAS;AAAA,EACT,aAAa,WAAW,MAAM,QAAQ;AAAA,EACtC,OAAO;AACT;AAKA,IAAM,WAAN,MAAM,kBAAiB,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,EAKnC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKhB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,EAKZ,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,EAKb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM5B,UAAU,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM3B,SAAS,IAAI,aAAa;AAAA,EAC1B;AAAA,EACA,IAAI,UAAU;AACZ,WAAO,KAAK,eAAe,IAAI,QAAQ,KAAK,SAAS,KAAK,UAAU,KAAK,YAAY,SAAS,KAAK,OAAO,KAAK,KAAK;AAAA,EACtH;AAAA,EACA,IAAI,iBAAiB;AACnB,WAAO;AAAA,MACL,0BAA0B;AAAA,MAC1B,kCAAkC,KAAK;AAAA,MACvC,cAAc,KAAK;AAAA,MACnB,oBAAoB,KAAK,YAAY,YAAY,KAAK,OAAO,WAAW,MAAM,YAAY,KAAK,OAAO,aAAa,MAAM;AAAA,MACzH,iCAAiC,KAAK,SAAS;AAAA,MAC/C,iCAAiC,KAAK,SAAS;AAAA,IACjD;AAAA,EACF;AAAA,EACA,iBAAiB,OAAO,MAAS;AAAA;AAAA;AAAA;AAAA;AAAA,EAKjC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,gBAAgB,MAAM;AAAA,EAAC;AAAA,EACvB,iBAAiB,MAAM;AAAA,EAAC;AAAA,EACxB,UAAU;AAAA,EACV,kBAAkB,OAAO,aAAa;AAAA,EACtC,qBAAqB;AACnB,SAAK,UAAU,QAAQ,UAAQ;AAC7B,cAAQ,KAAK,QAAQ,GAAG;AAAA,QACtB,KAAK;AACH,eAAK,wBAAwB,KAAK;AAClC;AAAA,QACF,KAAK;AACH,eAAK,wBAAwB,KAAK;AAClC;AAAA,MACJ;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,YAAY,SAAS;AACnB,UAAM,YAAY,OAAO;AACzB,QAAI,QAAQ,eAAe;AACzB,WAAK,eAAe,IAAI,QAAQ,cAAc,YAAY;AAAA,IAC5D;AAAA,EACF;AAAA,EACA,YAAY,OAAO;AACjB,QAAI;AAMJ,UAAM,cAAc,KAAK,SAAS,IAAI,WAAW,MAAM;AAAA,MACrD,UAAU;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AACD,UAAM,oBAAoB,eAAe,CAAC,KAAK,cAAc,YAAY,QAAQ,KAAK;AACtF,QAAI,CAAC,KAAK,QAAQ;AAChB,UAAI,KAAK,WAAW,KAAK,eAAe,EAAG,iBAAgB,kBAAkB,OAAO,SAAO,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC;AAAA,UAAO,iBAAgB,oBAAoB,CAAC,GAAG,mBAAmB,KAAK,KAAK,IAAI,CAAC,KAAK,KAAK;AAC9M,WAAK,cAAc,aAAa;AAChC,WAAK,QAAQ;AACb,UAAI,KAAK,aAAa;AACpB,aAAK,YAAY,SAAS,aAAa;AAAA,MACzC;AAAA,IACF,OAAO;AACL,sBAAgB,KAAK,eAAe,IAAI,KAAK,YAAY,KAAK,UAAU,KAAK,aAAa,KAAK;AAC/F,WAAK,QAAQ;AACb,WAAK,cAAc,aAAa;AAAA,IAClC;AACA,QAAI,KAAK,eAAe,GAAG;AACzB,WAAK,eAAe,IAAI,KAAK;AAAA,IAC/B;AACA,SAAK,SAAS,KAAK;AAAA,MACjB,SAAS;AAAA,MACT,eAAe;AAAA,IACjB,CAAC;AAAA,EACH;AAAA,EACA,aAAa,OAAO;AAClB,QAAI,CAAC,KAAK,UAAU;AAClB,WAAK,YAAY,KAAK;AAAA,IACxB;AAAA,EACF;AAAA,EACA,aAAa,OAAO;AAClB,SAAK,UAAU;AACf,SAAK,QAAQ,KAAK,KAAK;AAAA,EACzB;AAAA,EACA,YAAY,OAAO;AACjB,SAAK,UAAU;AACf,SAAK,OAAO,KAAK,KAAK;AACtB,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,QAAQ;AACN,SAAK,eAAe,cAAc,MAAM;AAAA,EAC1C;AAAA,EACA,WAAW,OAAO;AAChB,SAAK,QAAQ;AACb,SAAK,GAAG,aAAa;AAAA,EACvB;AAAA,EACA,iBAAiB,IAAI;AACnB,SAAK,gBAAgB;AAAA,EACvB;AAAA,EACA,kBAAkB,IAAI;AACpB,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,iBAAiB,KAAK;AACpB,eAAW,MAAM;AACf,WAAK,WAAW;AAChB,WAAK,GAAG,aAAa;AAAA,IACvB,CAAC;AAAA,EACH;AAAA,EACA,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,iBAAiB,mBAAmB;AAClD,cAAQ,0BAA0B,wBAA2B,sBAAsB,SAAQ,IAAI,qBAAqB,SAAQ;AAAA,IAC9H;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,YAAY,GAAG,CAAC,YAAY,GAAG,CAAC,aAAa,CAAC;AAAA,IAC3D,gBAAgB,SAAS,wBAAwB,IAAI,KAAK,UAAU;AAClE,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,KAAK,CAAC;AAClC,QAAG,eAAe,UAAU,eAAe,CAAC;AAAA,MAC9C;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,uBAAuB,GAAG;AAC3E,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,YAAY;AAAA,MAC/D;AAAA,IACF;AAAA,IACA,WAAW,SAAS,eAAe,IAAI,KAAK;AAC1C,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,KAAK,CAAC;AAAA,MACvB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,iBAAiB,GAAG;AAAA,MACvE;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,OAAO;AAAA,MACP,MAAM;AAAA,MACN,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,MACtD,QAAQ,CAAC,GAAG,UAAU,UAAU,gBAAgB;AAAA,MAChD,gBAAgB;AAAA,MAChB,WAAW;AAAA,MACX,UAAU,CAAC,GAAG,YAAY,YAAY,eAAe;AAAA,MACrD,SAAS;AAAA,MACT,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,eAAe,CAAC,GAAG,iBAAiB,iBAAiB,gBAAgB;AAAA,MACrE,MAAM;AAAA,MACN,aAAa;AAAA,MACb,cAAc;AAAA,MACd,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,MACtD,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,MACtD,WAAW,CAAC,GAAG,aAAa,aAAa,gBAAgB;AAAA,MACzD,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,SAAS;AAAA,IACX;AAAA,IACA,SAAS;AAAA,MACP,UAAU;AAAA,MACV,SAAS;AAAA,MACT,QAAQ;AAAA,IACV;AAAA,IACA,UAAU,CAAI,mBAAmB,CAAC,yBAAyB,aAAa,CAAC,GAAM,4BAA+B,oBAAoB;AAAA,IAClI,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,SAAS,EAAE,GAAG,CAAC,GAAG,SAAS,GAAG,CAAC,QAAQ,YAAY,GAAG,SAAS,QAAQ,UAAU,SAAS,WAAW,YAAY,YAAY,SAAS,GAAG,CAAC,GAAG,gBAAgB,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,oBAAoB,yBAAyB,GAAG,CAAC,GAAG,cAAc,GAAG,MAAM,GAAG,CAAC,SAAS,mBAAmB,GAAG,WAAW,GAAG,MAAM,GAAG,CAAC,GAAG,mBAAmB,GAAG,SAAS,GAAG,CAAC,GAAG,YAAY,CAAC;AAAA,IACvX,UAAU,SAAS,kBAAkB,IAAI,KAAK;AAC5C,UAAI,KAAK,GAAG;AACV,cAAM,MAAS,iBAAiB;AAChC,QAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,SAAS,GAAG,CAAC;AAC/C,QAAG,WAAW,SAAS,SAAS,yCAAyC,QAAQ;AAC/E,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,aAAa,MAAM,CAAC;AAAA,QAChD,CAAC,EAAE,QAAQ,SAAS,wCAAwC,QAAQ;AAClE,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,YAAY,MAAM,CAAC;AAAA,QAC/C,CAAC,EAAE,UAAU,SAAS,0CAA0C,QAAQ;AACtE,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,aAAa,MAAM,CAAC;AAAA,QAChD,CAAC;AACD,QAAG,aAAa;AAChB,QAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,QAAG,WAAW,GAAG,kCAAkC,GAAG,GAAG,gBAAgB,CAAC,EAAE,GAAG,qBAAqB,GAAG,GAAG,MAAM,CAAC;AACjH,QAAG,aAAa,EAAE;AAAA,MACpB;AACA,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,IAAI,KAAK;AACvB,QAAG,WAAW,IAAI,UAAU;AAC5B,QAAG,WAAW,WAAW,IAAI,cAAc;AAC3C,QAAG,YAAY,oBAAoB,IAAI,OAAO,EAAE,kBAAkB,IAAI,OAAO,EAAE,mBAAmB,IAAI,QAAQ;AAC9G,QAAG,UAAU;AACb,QAAG,WAAW,IAAI,UAAU;AAC5B,QAAG,WAAW,IAAI,UAAU;AAC5B,QAAG,WAAW,SAAS,IAAI,KAAK,EAAE,WAAW,IAAI,OAAO,EAAE,YAAY,IAAI,QAAQ,EAAE,YAAY,IAAI,QAAQ,EAAE,WAAc,gBAAgB,IAAI,GAAG,CAAC;AACpJ,QAAG,YAAY,MAAM,IAAI,OAAO,EAAE,QAAQ,IAAI,IAAI,EAAE,YAAY,IAAI,QAAQ,EAAE,YAAY,IAAI,WAAW,OAAO,IAAI,EAAE,mBAAmB,IAAI,cAAc,EAAE,cAAc,IAAI,SAAS;AACxL,QAAG,UAAU,CAAC;AACd,QAAG,WAAW,QAAQ,CAAC,IAAI,wBAAwB,CAAC,IAAI,qBAAqB;AAC7E,QAAG,UAAU;AACb,QAAG,WAAW,oBAAoB,IAAI,wBAAwB,IAAI,qBAAqB,EAAE,2BAA8B,gBAAgB,IAAI,KAAK,IAAI,OAAO,CAAC;AAAA,MAC9J;AAAA,IACF;AAAA,IACA,cAAc,CAAC,cAAiB,SAAY,MAAS,kBAAkB,WAAW,WAAW,YAAY;AAAA,IACzG,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,UAAU,CAAC;AAAA,IACjF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,SAAS,CAAC,cAAc,WAAW,WAAW,YAAY;AAAA,MAC1D,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAkCV,WAAW,CAAC,yBAAyB,aAAa;AAAA,MAClD,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,IACnC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,OAAO;AAAA,IAChB,CAAC;AAAA,IACD,sBAAsB,CAAC;AAAA,MACrB,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,QACrB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,OAAO,OAAO,SAAS,uBAAuB,mBAAmB;AAC/D,WAAO,KAAK,qBAAqB,iBAAgB;AAAA,EACnD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,UAAU,YAAY;AAAA,IAChC,SAAS,CAAC,UAAU,YAAY;AAAA,EAClC,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,UAAU,cAAc,YAAY;AAAA,EAChD,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,UAAU,YAAY;AAAA,MAChC,SAAS,CAAC,UAAU,YAAY;AAAA,IAClC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["CheckboxClasses"]}